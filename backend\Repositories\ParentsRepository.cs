using backend.Data;
using backend.Models;
using backend.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace backend.Repositories;

public class ParentsRepository : Repository<Parents>, IParentsRepository
{
    public ParentsRepository(ApplicationDbContext context)
        : base(context) { }

    public async Task<Parents> GetParentWithUsersAsync(Guid id)
    {
        return await _context.Parents.Include(p => p.Users).FirstOrDefaultAsync(p => p.Id == id);
    }

    public async Task<IEnumerable<Parents>> GetParentsWithUsersAsync()
    {
        return await _context.Parents.Include(p => p.Users).ToListAsync();
    }
}
