"use client";

import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { Button, Form, Input, message } from "antd";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { loginAdmin, isAuthenticated } from "@/services/api";

const AdminLogin = () => {
  const [loginForm] = Form.useForm();
  const [loginError, setLoginError] = useState("");
  const [loginLoading, setLoginLoading] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const router = useRouter();

  useEffect(() => {
    // Check if already authenticated
    if (isAuthenticated()) {
      router.push("/admin");
    }
  }, [router]);

  const handleLogin = async (values: { email: string, password: string }) => {
    setLoginLoading(true);
    setLoginError("");

    try {
      const response = await loginAdmin(values.email, values.password);
      
      if (response.token) {
        // Store token and authentication status
        localStorage.setItem('fbAdminToken', response.token);
        localStorage.setItem("fbAdmin", "true");
        
        messageApi.success("Giriş başarılı!");
        
        // Redirect to admin page
        setTimeout(() => {
          router.push("/admin");
        }, 1000);
      } else {
        setLoginError("Sunucudan geçersiz yanıt alındı.");
      }
    } catch (error: any) {
      console.error("Login error:", error);
      
      if (error.message.includes("Invalid email or password")) {
        setLoginError("E-posta veya şifre yanlış. Lütfen tekrar deneyin.");
      } else if (error.message) {
        setLoginError(error.message);
      } else {
        setLoginError("Giriş yapılırken bir hata oluştu. Lütfen tekrar deneyin.");
      }
    } finally {
      setLoginLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#0B478D] via-[#1A67A3] to-[#003366] flex items-center justify-center p-4">
      {contextHolder}
      <div className="max-w-md w-full">
        <div className="bg-white rounded-lg shadow-xl overflow-hidden">
          <div className="bg-gradient-to-r from-[#021F4A] to-[#0A4392] text-white p-6 relative overflow-hidden">
            <div className="absolute top-0 right-0 w-48 h-48 bg-[#FFED00] rounded-full opacity-10 transform translate-x-16 -translate-y-16"></div>
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-[#FFED00] rounded-full opacity-10 transform -translate-x-16 translate-y-8"></div>
            
            <div className="flex items-center gap-4">
              <Image 
                src="/logo.png" 
                alt="İzefe Logo" 
                width={60} 
                height={60} 
                className="relative z-10"
              />
              <div>
                <h1 className="text-xl md:text-2xl font-bold relative z-10 mb-1">İZEFE</h1>
                <h2 className="text-lg md:text-xl font-semibold relative z-10">Yönetici Paneli</h2>
              </div>
            </div>
          </div>
          
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-6">Giriş Yap</h3>
            
            {loginError && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm">
                {loginError}
              </div>
            )}
            
            <Form 
              form={loginForm}
              layout="vertical"
              onFinish={handleLogin}
            >
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: 'E-posta adresini girin' },
                  { type: 'email', message: 'Geçerli bir e-posta adresi girin' }
                ]}
              >
                <Input 
                  prefix={<UserOutlined className="site-form-item-icon" />} 
                  placeholder="E-posta" 
                  size="large"
                />
              </Form.Item>
              
              <Form.Item
                name="password"
                rules={[{ required: true, message: 'Şifrenizi girin' }]}
              >
                <Input.Password 
                  prefix={<LockOutlined className="site-form-item-icon" />}
                  placeholder="Şifre"
                  size="large"
                />
              </Form.Item>
              
              <Form.Item className="mb-0">
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  className="bg-gradient-to-r from-[#021F4A] to-[#0A4392] w-full"
                  size="large"
                  loading={loginLoading}
                >
                  Giriş Yap
                </Button>
              </Form.Item>
            </Form>
            
            <div className="mt-4 text-center text-sm text-gray-500">
              <p>Yönetici paneline erişim için giriş yapın.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminLogin;
