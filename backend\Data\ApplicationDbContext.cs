using backend.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace backend.Data
{
    public class ApplicationDbContext : IdentityDbContext<User, IdentityRole<Guid>, Guid>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options) { }

        public DbSet<User> Users { get; set; }
        public DbSet<Parents> Parents { get; set; }
        public DbSet<Rating> Ratings { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder
                .Entity<User>()
                .HasOne(u => u.Parent)
                .WithMany(p => p.Users)
                .HasForeignKey(u => u.ParentId)
                .OnDelete(DeleteBehavior.SetNull);

            // Rating relationships
            modelBuilder
                .Entity<Rating>()
                .HasOne(r => r.User)
                .WithMany()
                .HasForeignKey(r => r.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder
                .Entity<Rating>()
                .HasOne(r => r.Moderator)
                .WithMany()
                .HasForeignKey(r => r.ModeratorId)
                .OnDelete(DeleteBehavior.Restrict);

            // Ensure each moderator can only rate each user once
            modelBuilder
                .Entity<Rating>()
                .HasIndex(r => new { r.UserId, r.ModeratorId })
                .IsUnique();
        }
    }
}
