using backend.Models;

namespace backend.Repositories.Interfaces;

public interface IUserRepository : IRepository<User>
{
    Task<User> GetUserWithParentAsync(Guid id);
    Task<User> GetUserByTrackingCodeAsync(string trackingCode);
    Task<IEnumerable<User>> GetUsersWithParentsAsync();
    Task<IEnumerable<User>> GetApplicantUsersAsync(); // Only real applicants, excluding admin/moderator
    int GetNextTrackingCode();
    Task<int> GetNextTrackingSequenceForBirthYearAsync(int birthYear);
    Task DeleteUserWithParentAsync(Guid userId);
    Task<bool> EmailExistsAsync(string email);
    Task<User> GetUserByEmailAsync(string email);
}
