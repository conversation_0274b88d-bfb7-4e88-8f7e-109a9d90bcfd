"use client";

import { useState, useEffect } from "react";
import { Select, SelectItem } from "@/components/ui/select";
import { turkiyeApi, Province, District } from "@/services/turkiye-api";
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

interface LocationSelectProps {
  provinceValue?: string;
  districtValue?: string;
  onProvinceChange: (value: string) => void;
  onDistrictChange: (value: string) => void;
  provinceError?: string;
  districtError?: string;
}

export function LocationSelect({
  provinceValue,
  districtValue,
  onProvinceChange,
  onDistrictChange,
  provinceError,
  districtError,
}: LocationSelectProps) {
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [districts, setDistricts] = useState<District[]>([]);
  const [loadingProvinces, setLoadingProvinces] = useState(true);
  const [loadingDistricts, setLoadingDistricts] = useState(false);

  // Load provinces on component mount
  useEffect(() => {
    async function loadProvinces() {
      try {
        setLoadingProvinces(true);
        const data = await turkiyeApi.getProvinces();
        setProvinces(data);
      } catch (error) {
        console.error("Failed to load provinces:", error);
      } finally {
        setLoadingProvinces(false);
      }
    }

    loadProvinces();
  }, []);

  // Load districts when province changes
  useEffect(() => {
    async function loadDistricts() {
      if (!provinceValue) {
        setDistricts([]);
        return;
      }

      const selectedProvince = provinces.find((p) => p.name === provinceValue);
      if (!selectedProvince) return;

      try {
        setLoadingDistricts(true);
        const data = await turkiyeApi.getDistrictsByProvinceId(
          selectedProvince.id
        );
        setDistricts(data);
      } catch (error) {
        console.error("Failed to load districts:", error);
        setDistricts([]);
      } finally {
        setLoadingDistricts(false);
      }
    }

    loadDistricts();
    // Reset district selection when province changes
    if (districtValue) {
      onDistrictChange("");
    }
  }, [provinceValue, provinces]);

  const handleProvinceChange = (value: string) => {
    onProvinceChange(value);
    // Reset district when province changes
    if (districtValue) {
      onDistrictChange("");
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <FormItem>
        <FormLabel className="font-medium">İL</FormLabel>
        <FormControl>
          <Select
            value={provinceValue}
            onValueChange={handleProvinceChange}
            placeholder="İl seçiniz"
            disabled={loadingProvinces}
            
          >
            {provinces.map((province) => (
              <SelectItem key={province.id} value={province.name} >
                {province.name}
              </SelectItem>
            ))}
          </Select>
        </FormControl>
        {provinceError && <FormMessage>{provinceError}</FormMessage>}
      </FormItem>

      <FormItem>
        <FormLabel className="font-medium">İLÇE</FormLabel>
        <FormControl>
          <Select
            value={districtValue}
            onValueChange={onDistrictChange}
            placeholder="İlçe seçiniz"
            disabled={!provinceValue || loadingDistricts}
          >
            {districts.map((district) => (
              <SelectItem key={district.id} value={district.name}>
                {district.name}
              </SelectItem>
            ))}
          </Select>
        </FormControl>
        {districtError && <FormMessage>{districtError}</FormMessage>}
      </FormItem>
    </div>
  );
}
