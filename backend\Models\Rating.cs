using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace backend.Models;

/// <summary>
/// Represents a rating given by a moderator for a user
/// </summary>
public class Rating
{
    /// <summary>
    /// The unique identifier for the rating
    /// </summary>
    [Key]
    public Guid Id { get; set; }

    /// <summary>
    /// The ID of the user being rated
    /// </summary>
    [Required]
    public Guid UserId { get; set; }

    /// <summary>
    /// Navigation property to the user being rated
    /// </summary>
    [ForeignKey("UserId")]
    public User User { get; set; }

    /// <summary>
    /// The ID of the moderator giving the rating
    /// </summary>
    [Required]
    public Guid ModeratorId { get; set; }

    /// <summary>
    /// Navigation property to the moderator giving the rating
    /// </summary>
    [ForeignKey("ModeratorId")]
    public User Moderator { get; set; }

    /// <summary>
    /// Total skill score assigned by the moderator
    /// </summary>
    public int? ToplaBeceri { get; set; }

    /// <summary>
    /// Physical characteristics score assigned by the moderator
    /// </summary>
    public int? FizikselOzellik { get; set; }

    /// <summary>
    /// General notes about the user from the moderator
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// The jump height of the user in centimeters as recorded by the moderator
    /// </summary>
    public double? JumpHeight { get; set; }

    /// <summary>
    /// The date when the jump height was recorded by the moderator
    /// </summary>
    public DateTime? JumpRecordDate { get; set; }

    /// <summary>
    /// The 20-meter sprint time of the user in seconds as recorded by the moderator
    /// </summary>
    public double? SprintTime { get; set; }

    /// <summary>
    /// The date when the sprint time was recorded by the moderator
    /// </summary>
    public DateTime? SprintRecordDate { get; set; }

    /// <summary>
    /// Date when the rating was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Date when the rating was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
} 