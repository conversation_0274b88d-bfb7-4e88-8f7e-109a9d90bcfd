using backend.Data;
using backend.Models;
using backend.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace backend.Repositories;

public class RatingRepository : IRatingRepository
{
    private readonly ApplicationDbContext _context;

    public RatingRepository(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<Rating>> GetAllAsync()
    {
        return await _context.Ratings.Include(r => r.User).Include(r => r.Moderator).ToListAsync();
    }

    public async Task<Rating?> GetByIdAsync(Guid id)
    {
        return await _context
            .Ratings.Include(r => r.User)
            .Include(r => r.Moderator)
            .FirstOrDefaultAsync(r => r.Id == id);
    }

    public async Task<Rating?> GetByUserAndModeratorAsync(Guid userId, Guid moderatorId)
    {
        return await _context
            .Ratings.Include(r => r.User)
            .Include(r => r.Moderator)
            .FirstOrDefaultAsync(r => r.UserId == userId && r.ModeratorId == moderatorId);
    }

    public async Task<IEnumerable<Rating>> GetRatingsByUserAsync(Guid userId)
    {
        return await _context
            .Ratings.Include(r => r.User)
            .Include(r => r.Moderator)
            .Where(r => r.UserId == userId)
            .ToListAsync();
    }

    public async Task<IEnumerable<Rating>> GetRatingsByModeratorAsync(Guid moderatorId)
    {
        return await _context
            .Ratings.Include(r => r.User)
            .Include(r => r.Moderator)
            .Where(r => r.ModeratorId == moderatorId)
            .ToListAsync();
    }

    public async Task<Rating> AddAsync(Rating rating)
    {
        rating.Id = Guid.NewGuid();
        rating.CreatedAt = DateTime.UtcNow;
        rating.UpdatedAt = DateTime.UtcNow;

        _context.Ratings.Add(rating);
        return rating;
    }

    public async Task UpdateAsync(Rating rating)
    {
        rating.UpdatedAt = DateTime.UtcNow;
        _context.Entry(rating).State = EntityState.Modified;
    }

    public async Task DeleteAsync(Guid id)
    {
        var rating = await _context.Ratings.FindAsync(id);
        if (rating != null)
        {
            _context.Ratings.Remove(rating);
        }
    }

    public async Task<bool> ExistsAsync(Guid id)
    {
        return await _context.Ratings.AnyAsync(r => r.Id == id);
    }

    public async Task SaveAsync()
    {
        await _context.SaveChangesAsync();
    }
}
