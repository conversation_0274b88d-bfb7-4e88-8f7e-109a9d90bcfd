using System;
using System.ComponentModel.DataAnnotations;

namespace backend.Models.DTOs;

public class LoginDTO
{
    /// <summary>
    /// The email address of the user for login
    /// </summary>
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string Email { get; set; } // E-posta alanı

    /// <summary>
    /// The password for the user account
    /// </summary>
    [Required(ErrorMessage = "Password is required")]
    // [DataType(DataType.Password)] // Bu attribute genellikle frontend için kullanılır, backend validation için gerekli değil.
    public string Password { get; set; } // Parola alanı
}
