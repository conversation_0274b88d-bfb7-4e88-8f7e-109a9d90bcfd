using backend.Models;
using FluentValidation;

namespace backend.Validators
{
    public class ParentsValidator : AbstractValidator<Parents>
    {
        public ParentsValidator()
        {
            RuleFor(x => x.FullName)
                .NotEmpty()
                .When(x => !string.IsNullOrEmpty(x.FullName))
                .WithMessage("Ad ve soyad gereklidir");
            RuleFor(x => x.MobilePhone)
                .NotEmpty()
                .When(x => !string.IsNullOrEmpty(x.MobilePhone))
                .WithMessage("Cep telefonu gereklidir");
            
            RuleFor(x => x.RelationshipType)
                .NotEmpty()
                .When(x => !string.IsNullOrEmpty(x.RelationshipType))
                .WithMessage("Yakınlık derecesi gereklidir");
        }
    }
}

