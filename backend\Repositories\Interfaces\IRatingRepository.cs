using backend.Models;

namespace backend.Repositories.Interfaces;

public interface IRatingRepository
{
    Task<IEnumerable<Rating>> GetAllAsync();
    Task<Rating?> GetByIdAsync(Guid id);
    Task<Rating?> GetByUserAndModeratorAsync(Guid userId, Guid moderatorId);
    Task<IEnumerable<Rating>> GetRatingsByUserAsync(Guid userId);
    Task<IEnumerable<Rating>> GetRatingsByModeratorAsync(Guid moderatorId);
    Task<Rating> AddAsync(Rating rating);
    Task UpdateAsync(Rating rating);
    Task DeleteAsync(Guid id);
    Task<bool> ExistsAsync(Guid id);
    Task SaveAsync();
}
