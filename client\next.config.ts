import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  typescript: {
    ignoreBuildErrors: true,
  },
  // Add rewrites to ensure API requests go through proper paths
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: '/api/:path*',
      }
    ];
  },
  // Ensure environment variables are properly handled
  env: {
    // No hard-coded API_URL, rely on relative paths
  }
};

export default nextConfig;
