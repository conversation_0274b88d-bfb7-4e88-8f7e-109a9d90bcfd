const API_BASE_URL = "https://turkiyeapi.dev/api/v1";

export interface Province {
  id: number;
  name: string;
  population: number;
  area: number;
  altitude: number;
  areaCode: number[];
  isCoastal: boolean;
  isMetropolitan: boolean;
  districts: District[];
}

export interface District {
  id: number;
  name: string;
  population: number;
  area: number;
}

export interface ApiResponse<T> {
  status: string;
  data: T;
}

export const turkiyeApi = {
  /**
   * Get all provinces
   */
  async getProvinces(): Promise<Province[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/provinces`);
      const data: ApiResponse<Province[]> = await response.json();
      return data.data;
    } catch (error) {
      console.error("Error fetching provinces:", error);
      throw error;
    }
  },

  /**
   * Get a specific province by ID with districts
   */
  async getProvinceById(id: number): Promise<Province> {
    try {
      const response = await fetch(`${API_BASE_URL}/provinces/${id}`);
      const data: ApiResponse<Province> = await response.json();
      return data.data;
    } catch (error) {
      console.error("Error fetching province:", error);
      throw error;
    }
  },

  /**
   * Get districts by province ID
   */
  async getDistrictsByProvinceId(provinceId: number): Promise<District[]> {
    try {
      const response = await fetch(
        `${API_BASE_URL}/districts?provinceId=${provinceId}`
      );
      const data: ApiResponse<District[]> = await response.json();
      return data.data;
    } catch (error) {
      console.error("Error fetching districts:", error);
      throw error;
    }
  },
};
