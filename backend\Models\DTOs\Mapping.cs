using System;
using System.Collections.Generic;
using System.Linq;

namespace backend.Models.DTOs
{
    public static class Mapping
    {
        // Convert User to UserWithParentDTO
        public static UserWithParentDTO ToUserWithParentDTO(User user)
        {
            if (user == null)
                return null!;

            return new UserWithParentDTO
            {
                Id = user.Id,
                FullName = user.FullName,
                DateOfBirth = user.DateOfBirth,
                Height = user.Height,
                Weight = user.Weight,
                PhoneNumber = user.PhoneNumber,
                Email = user.Email,
                Province = user.Province,
                District = user.District,
                ArmSpan = user.ArmSpan,
                ShoeSize = user.ShoeSize,
                PalmSize = user.PalmSize,
                HasPlayedForClub = user.HasPlayedForClub,
                ClubName = user.ClubName,
                HasHealthIssues = user.HasHealthIssues,
                HealthIssues = user.HealthIssues,
                ParentId = user.ParentId ?? Guid.Empty,
                TrackingCode = user.TrackingCode,
                JumpHeight = user.JumpHeight,
                JumpRecordDate = user.JumpRecordDate,
                Status = user.Status,
                Parent = user.Parent != null ? ToParentDTO(user.Parent) : null,
            };
        }

        // Convert List of Users to List of UserWithParentDTOs
        public static IEnumerable<UserWithParentDTO> ToUserWithParentDTOs(IEnumerable<User> users)
        {
            return users.Select(u => ToUserWithParentDTO(u));
        }

        // Convert Parents to ParentDTO
        public static ParentDTO ToParentDTO(Parents parent)
        {
            if (parent == null)
                return null!;
            return new ParentDTO
            {
                Id = parent.Id,
                FullName = parent.FullName,
                MobilePhone = parent.MobilePhone,
                MotherHeight = parent.MotherHeight,
                FatherHeight = parent.FatherHeight,
                RelationshipType = parent.RelationshipType,
            };
        }

        // Convert List of Parents to List of ParentDTOs
        public static IEnumerable<ParentDTO> ToParentDTOs(IEnumerable<Parents> parents)
        {
            return parents.Select(p => ToParentDTO(p));
        }

        // Update User from UserWithParentDTO
        public static void UpdateUserFromDTO(User user, UserWithParentDTO dto)
        {
            if (!string.IsNullOrEmpty(dto.FullName))
                user.FullName = dto.FullName;
            user.DateOfBirth = dto.DateOfBirth;
            if (dto.Height.HasValue)
                user.Height = dto.Height.Value;
            if (dto.Weight.HasValue)
                user.Weight = dto.Weight.Value;
            if (!string.IsNullOrEmpty(dto.PhoneNumber))
                user.PhoneNumber = dto.PhoneNumber;
            if (!string.IsNullOrEmpty(dto.Email))
                user.Email = dto.Email;
            user.Province = dto.Province;
            user.District = dto.District;
            user.ArmSpan = dto.ArmSpan;
            if (dto.ShoeSize.HasValue)
                user.ShoeSize = dto.ShoeSize.Value;
            user.PalmSize = dto.PalmSize;
            user.HasPlayedForClub = dto.HasPlayedForClub;
            user.ClubName = dto.ClubName ?? string.Empty;
            user.HasHealthIssues = dto.HasHealthIssues;
            user.HealthIssues = dto.HealthIssues ?? string.Empty;
            user.JumpHeight = dto.JumpHeight;
            user.JumpRecordDate = dto.JumpRecordDate;
            user.Status = dto.Status;
        }

        public static void UpdateParentFromDTO(Parents parent, ParentDTO dto)
        {
            if (!string.IsNullOrEmpty(dto.FullName))
                parent.FullName = dto.FullName;
            if (!string.IsNullOrEmpty(dto.MobilePhone))
                parent.MobilePhone = dto.MobilePhone;
            if (dto.MotherHeight.HasValue)
                parent.MotherHeight = dto.MotherHeight.Value;
            if (dto.FatherHeight.HasValue)
                parent.FatherHeight = dto.FatherHeight.Value;
            if (!string.IsNullOrEmpty(dto.RelationshipType))
                parent.RelationshipType = dto.RelationshipType;
        }
    }
}
