using System;
using System.ComponentModel.DataAnnotations;

namespace backend.Models.DTOs
{
    public class UserWithParentDTO
    {
        public Guid Id { get; set; }
        public string? FullName { get; set; }
        public DateTime DateOfBirth { get; set; }
        public double? Height { get; set; }
        public double? Weight { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public string? Province { get; set; }
        public string? District { get; set; }
        public double? ArmSpan { get; set; }
        public int? ShoeSize { get; set; }
        public double? PalmSize { get; set; }
        public bool HasPlayedForClub { get; set; }
        public string? ClubName { get; set; }
        public bool HasHealthIssues { get; set; }
        public string? HealthIssues { get; set; }
        public Guid ParentId { get; set; }

        public string? TrackingCode { get; set; }

        public ParentDTO? Parent { get; set; }

        /// <summary>
        /// The jump height of the user in centimeters
        /// </summary>
        public double? JumpHeight { get; set; }

        /// <summary>
        /// The date when the jump height was recorded
        /// </summary>
        public DateTime? JumpRecordDate { get; set; }

        /// <summary>
        /// Status of the user selection process
        /// 0: Not Decided, 1: Selected, 2: Not Selected
        /// </summary>
        public int? Status { get; set; }
    }
}
