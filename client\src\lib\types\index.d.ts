type UserWithParentDTO = {
  id: number;
  fullName: string;
  dateOfBirth: string;
  height: number;
  weight: number;
  phoneNumber: string;
  email: string;
  armSpan: number;
  shoeSize: number;
  palmSize: number;
  hasPlayedForClub: boolean;
  clubName: string;
  hasHealthIssues: boolean;
  healthIssues: string;
  parentId: number;
  parent?: ParentDTO;
  trackingCode: string;
  jumpHeight?: number;
  jumpRecordDate?: string;
  status?: number;
};

type ParentDTO = {
  id: number;
  fullName: string;
  mobilePhone: string;
  motherHeight: number;
  fatherHeight: number;
  relationshipType: string;
  approvalGiven: boolean;
};
