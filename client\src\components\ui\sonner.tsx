"use client"

import { useTheme } from "next-themes"
import { Toaster as Son<PERSON> } from "sonner"

export function Toaster(props: React.ComponentProps<typeof Sonner>) {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as "light" | "dark" | "system"}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast: "group border-2 rounded-lg shadow-lg",
          success: "!bg-[#EFF6FF] !text-[#021F4A] !border-[#021F4A] !border-l-4",
          error: "!bg-[#FEF2F2] !text-red-700 !border-red-600 !border-l-4",
          info: "!bg-[#EFF6FF] !text-[#0A4392] !border-[#0A4392] !border-l-4",
          warning: "!bg-[#FFFBEB] !text-amber-700 !border-amber-500 !border-l-4",
          title: "text-base font-semibold",
          description: "text-sm font-medium !text-green-500 rounded mt-1 ",
          actionButton: "bg-[#021F4A] text-white",
          cancelButton: "bg-gray-100 text-gray-700",
          icon: "text-3xl",
        },
      }}
      {...props}
    />
  )
}
