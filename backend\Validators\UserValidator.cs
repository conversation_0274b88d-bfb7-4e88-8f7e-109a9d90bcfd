using backend.Models;
using backend.Repositories.Interfaces;
using FluentValidation;

namespace backend.Validators
{
    public class UserValidator : AbstractValidator<User>
    {
        private readonly IUserRepository _userRepository;

        public UserValidator(IUserRepository userRepository)
        {
            _userRepository = userRepository;

            RuleFor(x => x.FullName).NotEmpty().WithMessage("Ad ve soyad gereklidir");
            RuleFor(x => x.DateOfBirth).NotEmpty().WithMessage("Doğum tarihi gereklidir");
            
            RuleFor(x => x.PhoneNumber)
                .NotEmpty()
                .When(x => !string.IsNullOrEmpty(x.PhoneNumber))
                .WithMessage("Telefon numarası gereklidir");
            
            RuleFor(x => x.Email)
                .NotEmpty()
                .EmailAddress()
                .When(x => !string.IsNullOrEmpty(x.Email))
                .WithMessage("Geç<PERSON>li bir e-posta adresi giriniz")
                .MustAsync(async (email, cancellation) => 
                {
                    if (string.IsNullOrEmpty(email)) return true;
                    return !await _userRepository.EmailExistsAsync(email);
                })
                .WithMessage("Bu e-posta adresi ile daha önce kayıt yapılmıştır.");
        }
    }
}
