using backend.Models;
using backend.Models.DTOs;
using backend.Repositories.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace backend.Controllers;

/// <summary>
/// Controller for managing parents data
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class ParentsController(IParentsRepository parentsRepository) : ControllerBase
{
    private readonly IParentsRepository _parentsRepository = parentsRepository;

    /// <summary>
    /// Gets all parents with their associated users
    /// </summary>
    /// <returns>A list of all parents with their users</returns>
    /// <response code="200">Returns the list of parents</response>
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ParentDTO>>> GetParents()
    {
        var parents = await _parentsRepository.GetParentsWithUsersAsync();
        return Ok(Mapping.ToParentDTOs(parents));
    }

    /// <summary>
    /// Gets a specific parent by ID with their associated users
    /// </summary>
    /// <param name="id">The ID of the parent to retrieve</param>
    /// <returns>The parent with the specified ID</returns>
    /// <response code="200">Returns the requested parent</response>
    /// <response code="404">If the parent is not found</response>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ParentDTO>> GetParent(Guid id)
    {
        var parent = await _parentsRepository.GetParentWithUsersAsync(id);

        if (parent == null)
        {
            return NotFound();
        }

        return Ok(Mapping.ToParentDTO(parent));
    }

    /// <summary>
    /// Creates a new parent
    /// </summary>
    /// <param name="parentDto">The parent to create</param>
    /// <returns>The newly created parent</returns>
    /// <response code="201">Returns the newly created parent</response>
    /// <response code="400">If the parent data is invalid</response>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ParentDTO>> PostParent(ParentDTO parentDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        // Create new Parents entity from DTO using mapping approach
        var parent = new Parents
        {
            FullName = parentDto.FullName,
            MobilePhone = parentDto.MobilePhone,
            RelationshipType = parentDto.RelationshipType,
            ApprovalGiven = false,
            ApprovalDate = null,
        };

        // Use mapping to update other parent properties from DTO
        Mapping.UpdateParentFromDTO(parent, parentDto);

        await _parentsRepository.AddAsync(parent);
        await _parentsRepository.SaveAsync();

        // Get the complete parent for the response
        var createdParent = await _parentsRepository.GetParentWithUsersAsync(parent.Id);
        var createdParentDto = Mapping.ToParentDTO(createdParent);

        return CreatedAtAction(nameof(GetParent), new { id = parent.Id }, createdParentDto);
    }

    /// <summary>
    /// Updates a specific parent
    /// </summary>
    /// <param name="id">The ID of the parent to update</param>
    /// <param name="parentDto">The updated parent data</param>
    /// <returns>No content</returns>
    /// <response code="204">If the update was successful</response>
    /// <response code="400">If the ID in the URL doesn't match the ID in the parent object</response>
    /// <response code="404">If the parent is not found</response>
    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> PutParent(Guid id, ParentDTO parentDto)
    {
        if (id != parentDto.Id)
        {
            return BadRequest();
        }

        try
        {
            var existingParent = await _parentsRepository.GetByIdAsync(id);
            if (existingParent == null)
            {
                return NotFound();
            }

            // Update parent properties from DTO
            Mapping.UpdateParentFromDTO(existingParent, parentDto);

            await _parentsRepository.UpdateAsync(existingParent);
            await _parentsRepository.SaveAsync();
        }
        catch (Exception)
        {
            if (!await _parentsRepository.ExistsAsync(id))
            {
                return NotFound();
            }
            else
            {
                throw;
            }
        }

        return NoContent();
    }

    /// <summary>
    /// Deletes a specific parent
    /// </summary>
    /// <param name="id">The ID of the parent to delete</param>
    /// <returns>No content</returns>
    /// <response code="204">If the deletion was successful</response>
    /// <response code="404">If the parent is not found</response>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeleteParent(Guid id)
    {
        var parent = await _parentsRepository.GetByIdAsync(id);
        if (parent == null)
        {
            return NotFound();
        }

        await _parentsRepository.DeleteAsync(parent.Id);
        await _parentsRepository.SaveAsync();

        return NoContent();
    }

    /// <summary>
    /// Sets the approval status for a parent
    /// </summary>
    /// <param name="id">The ID of the parent</param>
    /// <param name="approved">Whether the parent approves the registration</param>
    /// <returns>No content</returns>
    /// <response code="204">If the approval status was updated successfully</response>
    /// <response code="404">If the parent is not found</response>
    [HttpPut("{id}/approve")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ApproveParent(Guid id, [FromBody] bool approved)
    {
        var parent = await _parentsRepository.GetByIdAsync(id);
        if (parent == null)
        {
            return NotFound();
        }

        parent.ApprovalGiven = approved;
        if (approved && !parent.ApprovalDate.HasValue)
        {
            parent.ApprovalDate = DateTime.UtcNow;
        }

        await _parentsRepository.UpdateAsync(parent);
        await _parentsRepository.SaveAsync();

        return NoContent();
    }
}
