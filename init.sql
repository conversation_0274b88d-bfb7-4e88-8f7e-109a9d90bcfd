-- PostgreSQL initialization script for Fenerbahçe application
-- This script runs automatically when the PostgreSQL container starts for the first time

-- Create database if it doesn't exist (though POSTGRES_DB env var handles this)
-- You can add your table creation scripts, initial data, or other setup here

-- Example: Ensure the database has proper encoding
-- ALTER DATABASE fb SET search_path TO public;

-- Add any initial setup queries here as needed
-- CREATE TABLE IF NOT EXISTS your_table_name (...);

-- This file is optional - remove the volume mount from docker-compose.yml if not needed 