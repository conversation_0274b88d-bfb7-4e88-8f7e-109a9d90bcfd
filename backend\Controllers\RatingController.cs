using System.Security.Claims;
using backend.Models;
using backend.Models.DTOs;
using backend.Repositories.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace backend.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class RatingController : ControllerBase
{
    private readonly IRatingRepository _ratingRepository;
    private readonly UserManager<User> _userManager;

    public RatingController(IRatingRepository ratingRepository, UserManager<User> userManager)
    {
        _ratingRepository = ratingRepository;
        _userManager = userManager;
    }

    /// <summary>
    /// Get all ratings (Admin only)
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<IEnumerable<UserRatingSummaryDTO>>> GetAllRatings()
    {
        var ratings = await _ratingRepository.GetAllAsync();
        var groupedRatings = ratings
            .GroupBy(r => r.UserId)
            .Select(g => new UserRatingSummaryDTO
            {
                UserId = g.Key,
                UserName = g.First().User.FullName,
                Ratings = g.Select(r => new RatingWithModeratorDTO
                    {
                        Id = r.Id,
                        UserId = r.UserId,
                        ModeratorId = r.ModeratorId,
                        ModeratorName = r.Moderator.FullName,
                        ToplaBeceri = r.ToplaBeceri,
                        FizikselOzellik = r.FizikselOzellik,
                        Notes = r.Notes,
                        JumpHeight = r.JumpHeight,
                        JumpRecordDate = r.JumpRecordDate,
                        SprintTime = r.SprintTime,
                        SprintRecordDate = r.SprintRecordDate,
                        CreatedAt = r.CreatedAt,
                        UpdatedAt = r.UpdatedAt,
                    })
                    .ToList(),
                AverageToplaBeceri = g.Where(r => r.ToplaBeceri.HasValue).Any()
                    ? g.Where(r => r.ToplaBeceri.HasValue).Average(r => r.ToplaBeceri.Value)
                    : null,
                AverageFizikselOzellik = g.Where(r => r.FizikselOzellik.HasValue).Any()
                    ? g.Where(r => r.FizikselOzellik.HasValue).Average(r => r.FizikselOzellik.Value)
                    : null,
                RatingCount = g.Count(),
            })
            .ToList();

        return Ok(groupedRatings);
    }

    /// <summary>
    /// Get ratings for a specific user (Admin only)
    /// </summary>
    [HttpGet("user/{userId}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<UserRatingSummaryDTO>> GetUserRatings(Guid userId)
    {
        var ratings = await _ratingRepository.GetRatingsByUserAsync(userId);

        if (!ratings.Any())
        {
            return NotFound("No ratings found for this user");
        }

        var userRatingSummary = new UserRatingSummaryDTO
        {
            UserId = userId,
            UserName = ratings.First().User.FullName,
            Ratings = ratings
                .Select(r => new RatingWithModeratorDTO
                {
                    Id = r.Id,
                    UserId = r.UserId,
                    ModeratorId = r.ModeratorId,
                    ModeratorName = r.Moderator.FullName,
                    ToplaBeceri = r.ToplaBeceri,
                    FizikselOzellik = r.FizikselOzellik,
                    Notes = r.Notes,
                    CreatedAt = r.CreatedAt,
                    UpdatedAt = r.UpdatedAt,
                })
                .ToList(),
            AverageToplaBeceri = ratings.Where(r => r.ToplaBeceri.HasValue).Any()
                ? ratings.Where(r => r.ToplaBeceri.HasValue).Average(r => r.ToplaBeceri.Value)
                : null,
            AverageFizikselOzellik = ratings.Where(r => r.FizikselOzellik.HasValue).Any()
                ? ratings
                    .Where(r => r.FizikselOzellik.HasValue)
                    .Average(r => r.FizikselOzellik.Value)
                : null,
            RatingCount = ratings.Count(),
        };

        return Ok(userRatingSummary);
    }

    /// <summary>
    /// Get current moderator's ratings
    /// </summary>
    [HttpGet("my-ratings")]
    [Authorize(Roles = "Moderator,Admin")]
    public async Task<ActionResult<IEnumerable<RatingDTO>>> GetMyRatings()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId) || !Guid.TryParse(userId, out var moderatorId))
        {
            return Unauthorized();
        }

        var ratings = await _ratingRepository.GetRatingsByModeratorAsync(moderatorId);
        var ratingDTOs = ratings
            .Select(r => new RatingDTO
            {
                Id = r.Id,
                UserId = r.UserId,
                FullName = r.User.FullName,
                ModeratorId = r.ModeratorId,
                ToplaBeceri = r.ToplaBeceri,
                FizikselOzellik = r.FizikselOzellik,
                Notes = r.Notes,
                JumpHeight = r.JumpHeight,
                JumpRecordDate = r.JumpRecordDate,
                SprintTime = r.SprintTime,
                SprintRecordDate = r.SprintRecordDate,
                CreatedAt = r.CreatedAt,
                UpdatedAt = r.UpdatedAt,
            })
            .ToList();

        return Ok(ratingDTOs);
    }

    /// <summary>
    /// Get current moderator's rating for a specific user
    /// </summary>
    [HttpGet("my-rating/{userId}")]
    [Authorize(Roles = "Moderator,Admin")]
    public async Task<ActionResult<RatingDTO>> GetMyRatingForUser(Guid userId)
    {
        var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (
            string.IsNullOrEmpty(currentUserId)
            || !Guid.TryParse(currentUserId, out var moderatorId)
        )
        {
            return Unauthorized();
        }

        var rating = await _ratingRepository.GetByUserAndModeratorAsync(userId, moderatorId);

        if (rating == null)
        {
            return NotFound();
        }

        var ratingDTO = new RatingDTO
        {
            Id = rating.Id,
            UserId = rating.UserId,
            ModeratorId = rating.ModeratorId,
            ToplaBeceri = rating.ToplaBeceri,
            FizikselOzellik = rating.FizikselOzellik,
            Notes = rating.Notes,
            JumpHeight = rating.JumpHeight,
            JumpRecordDate = rating.JumpRecordDate,
            SprintTime = rating.SprintTime,
            SprintRecordDate = rating.SprintRecordDate,
            CreatedAt = rating.CreatedAt,
            UpdatedAt = rating.UpdatedAt,
        };

        return Ok(ratingDTO);
    }

    /// <summary>
    /// Create or update rating for a user
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Moderator,Admin")]
    public async Task<ActionResult<RatingDTO>> CreateOrUpdateRating(CreateRatingDTO createRatingDto)
    {
        var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (
            string.IsNullOrEmpty(currentUserId)
            || !Guid.TryParse(currentUserId, out var moderatorId)
        )
        {
            return Unauthorized();
        }

        // Check if rating already exists
        var existingRating = await _ratingRepository.GetByUserAndModeratorAsync(
            createRatingDto.UserId,
            moderatorId
        );

        if (existingRating != null)
        {
            // Update existing rating
            existingRating.ToplaBeceri = createRatingDto.ToplaBeceri;
            existingRating.FizikselOzellik = createRatingDto.FizikselOzellik;
            existingRating.Notes = createRatingDto.Notes;
            existingRating.JumpHeight = createRatingDto.JumpHeight;
            existingRating.JumpRecordDate = createRatingDto.JumpRecordDate;
            existingRating.SprintTime = createRatingDto.SprintTime;
            existingRating.SprintRecordDate = createRatingDto.SprintRecordDate;

            await _ratingRepository.UpdateAsync(existingRating);
            await _ratingRepository.SaveAsync();

            var updatedRatingDTO = new RatingDTO
            {
                Id = existingRating.Id,
                UserId = existingRating.UserId,
                ModeratorId = existingRating.ModeratorId,
                ToplaBeceri = existingRating.ToplaBeceri,
                FizikselOzellik = existingRating.FizikselOzellik,
                Notes = existingRating.Notes,
                CreatedAt = existingRating.CreatedAt,
                UpdatedAt = existingRating.UpdatedAt,
            };

            return Ok(updatedRatingDTO);
        }
        else
        {
            // Create new rating
            var newRating = new Rating
            {
                UserId = createRatingDto.UserId,
                ModeratorId = moderatorId,
                ToplaBeceri = createRatingDto.ToplaBeceri,
                FizikselOzellik = createRatingDto.FizikselOzellik,
                Notes = createRatingDto.Notes,
                JumpHeight = createRatingDto.JumpHeight,
                JumpRecordDate = createRatingDto.JumpRecordDate,
                SprintTime = createRatingDto.SprintTime,
                SprintRecordDate = createRatingDto.SprintRecordDate,
            };

            await _ratingRepository.AddAsync(newRating);
            await _ratingRepository.SaveAsync();

            var newRatingDTO = new RatingDTO
            {
                Id = newRating.Id,
                UserId = newRating.UserId,
                ModeratorId = newRating.ModeratorId,
                ToplaBeceri = newRating.ToplaBeceri,
                FizikselOzellik = newRating.FizikselOzellik,
                Notes = newRating.Notes,
                JumpHeight = newRating.JumpHeight,
                JumpRecordDate = newRating.JumpRecordDate,
                SprintTime = newRating.SprintTime,
                SprintRecordDate = newRating.SprintRecordDate,
                CreatedAt = newRating.CreatedAt,
                UpdatedAt = newRating.UpdatedAt,
            };

            return CreatedAtAction(
                nameof(GetMyRatingForUser),
                new { userId = newRating.UserId },
                newRatingDTO
            );
        }
    }

    /// <summary>
    /// Delete a rating (Admin only)
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin,Moderator")]
    public async Task<IActionResult> DeleteRating(Guid id)
    {
        var rating = await _ratingRepository.GetByIdAsync(id);
        if (rating == null)
        {
            return NotFound();
        }

        // Admin her şeyi silebilir, moderator sadece kendi puanını silebilir
        var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var isAdmin = User.IsInRole("Admin");
        if (!isAdmin)
        {
            if (string.IsNullOrEmpty(currentUserId) || !Guid.TryParse(currentUserId, out var moderatorId))
            {
                return Unauthorized();
            }
            if (rating.ModeratorId != moderatorId)
            {
                return Forbid();
            }
        }

        await _ratingRepository.DeleteAsync(id);
        await _ratingRepository.SaveAsync();

        return NoContent();
    }
}
