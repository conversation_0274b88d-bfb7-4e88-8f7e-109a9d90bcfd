using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Identity;

namespace backend.Models;

/// <summary>
/// Represents a user registered in the Fenerbahçe form system
/// </summary>
public class User : IdentityUser<Guid>
{
    /// <summary>
    /// The unique identifier for the user
    /// </summary>
    // [Key]
    // public int Id { get; set; }

    /// <summary>
    /// The full name of the user
    /// </summary>
    [Required(ErrorMessage = "Full name is required")]
    public string FullName { get; set; }

    /// <summary>
    /// The date of birth of the user
    /// </summary>
    [Required(ErrorMessage = "Date of birth is required")]
    public DateTime DateOfBirth { get; set; }

    /// <summary>
    /// The height of the user in centimeters
    /// </summary>
    public double Height { get; set; }

    /// <summary>
    /// The weight of the user in kilograms
    /// </summary>
    public double Weight { get; set; }

    /// <summary>
    /// The phone number of the user
    /// </summary>
    // [Required(ErrorMessage = "Phone number is required")]
    // [Phone(ErrorMessage = "Invalid phone number format")]
    // public string Phone { get; set; }

    /// <summary>
    /// The email address of the user
    /// </summary>
    // [Required(ErrorMessage = "Email is required")]
    // [EmailAddress(ErrorMessage = "Invalid email format")]
    // public string Email { get; set; }

    /// <summary>
    /// The province (il) where the user lives
    /// </summary>
    public string? Province { get; set; }

    /// <summary>
    /// The district (ilçe) where the user lives
    /// </summary>
    public string? District { get; set; }

    /// <summary>
    /// The arm span/wingspan of the user in centimeters
    /// </summary>
    public double? ArmSpan { get; set; }

    /// <summary>
    /// The shoe size of the user (EU standard)
    /// </summary>
    public int ShoeSize { get; set; }

    /// <summary>
    /// The palm size of the user in centimeters
    /// </summary>
    public double? PalmSize { get; set; }

    /// <summary>
    /// Indicates whether the user has played for a club before
    /// </summary>
    public bool HasPlayedForClub { get; set; }

    /// <summary>
    /// The name of the club the user has played for, if applicable
    /// </summary>
    public string ClubName { get; set; }

    /// <summary>
    /// Indicates whether the user has any health issues
    /// </summary>
    public bool HasHealthIssues { get; set; }

    /// <summary>
    /// Description of health issues, if applicable
    /// </summary>
    public string HealthIssues { get; set; }

    /// <summary>
    /// The ID of the parent associated with this user
    /// </summary>
    // [Required(ErrorMessage = "Parent ID is required")]
    public Guid? ParentId { get; set; }

    /// <summary>
    /// Navigation property to the parent of this user
    /// </summary>
    [ForeignKey("ParentId")]
    [JsonIgnore] // Add this to prevent serialization issues
    public Parents? Parent { get; set; } // Make it nullable to prevent validation errors

    /// <summary>
    /// A sequential number used for tracking code generation per birth year.
    /// Each birth year starts from 1 and increments for each user born in that year.
    /// This value should be calculated and set when creating a new user based on
    /// the count of existing users with the same birth year.
    /// </summary>
    public int TrackingSequence { get; set; }

    /// <summary>
    /// A unique tracking code generated for this user
    /// Format: {LastTwoDigitsOfBirthYear}{SequenceNumber:D3}
    /// Example: 11001 for birth year 2011, sequence 1 (first person born in 2011)
    /// Example: 11002 for birth year 2011, sequence 2 (second person born in 2011)
    /// Example: 12001 for birth year 2012, sequence 1 (first person born in 2012)
    /// Note: TrackingSequence should be calculated per birth year, starting from 1 for each year
    /// </summary>
    [NotMapped]
    public string TrackingCode => $"{DateOfBirth.Year % 100:D2}{TrackingSequence:D3}";

    /// <summary>
    /// The jump height of the user in centimeters
    /// </summary>
    public double? JumpHeight { get; set; }

    /// <summary>
    /// The date when the jump height was recorded
    /// </summary>
    public DateTime? JumpRecordDate { get; set; }

    public int? Status { get; set; } = -1; // -1: seçim yapılmadı (default), 0: seçmeye katılmadı, 1: takıma seçildi, 2: takıma seçilmedi
}
