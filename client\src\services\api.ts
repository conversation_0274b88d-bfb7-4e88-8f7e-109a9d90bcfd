import { API_BASE_URL, apiEndpoints, fetchApi } from "@/config/api";

/**
 * Submit form data to the backend
 */
export async function submitFormToBackend(formData: any) {
  try {
    // Prepare relationship type (handle custom relationship type)
    const relationshipType =
      formData.parentRelationshipType === "Diğer"
        ? formData.customRelationshipType
        : formData.parentRelationshipType;

    // Create the request body
    const requestBody = {
      user: {
        fullName: formData.fullName,
        dateOfBirth: formData.birthDate,
        height: Number(formData.height),
        weight: Number(formData.weight),
        phoneNumber: formData.phoneNumber,
        email: formData.email,
        province: formData.province || null,
        district: formData.district || null,
        armSpan: formData.armSpan ? Number(formData.armSpan) : null,
        shoeSize: Number(formData.shoeSize),
        palmSize: formData.palmSize ? Number(formData.palmSize) : null,
        jumpHeight: formData.jumpHeight ? Number(formData.jumpHeight) : null,
        jumpRecordDate: formData.jumpRecordDate,
        hasPlayedForClub: formData.hasClub,
        clubName: formData.clubName || "",
        hasHealthIssues: formData.hasChronicIllness,
        healthIssues: formData.chronicIllnessDetails || "",
        // Status is not included as it's managed by admin only
      },
      parent: {
        fullName: formData.parentFullName,
        mobilePhone: formData.parentPhone,
        relationshipType: relationshipType,
        motherHeight: Number(formData.motherHeight),
        fatherHeight: Number(formData.fatherHeight),
        approvalGiven: formData.parentConsent,
        approvalDate: new Date().toISOString(),
      },
    };

    const response = await fetchApi(apiEndpoints.submitForm, {
      method: "POST",
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw { response: { data: errorData } };
    }

    return await response.json();
  } catch (error) {
    console.error("Form submission error:", error);
    throw error;
  }
}

/**
 * Fetch all applications from the backend
 */
export async function fetchApplications() {
  try {
    const response = await fetchApi(apiEndpoints.getApplications, {
      method: "GET",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Başvuruları alırken bir hata oluştu"
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Fetch applications error:", error);
    throw error;
  }
}

/**
 * Fetch a specific application by ID
 */
export async function fetchApplicationById(id: string) {
  try {
    const response = await fetchApi(`${apiEndpoints.getApplications}/${id}`, {
      method: "GET",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Başvuru alınırken bir hata oluştu");
    }

    return await response.json();
  } catch (error) {
    console.error("Fetch application error:", error);
    throw error;
  }
}

/**
 * Update application status
 */
export async function updateApplicationStatus(id: string, status: string) {
  try {
    const response = await fetchApi(`${apiEndpoints.updateStatus}/${id}`, {
      method: "PATCH",
      body: JSON.stringify({ status }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Durum güncellenirken bir hata oluştu"
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Update status error:", error);
    throw error;
  }
}

/**
 * Login admin user and get token
 */
export async function loginAdmin(email: string, password: string) {
  try {
    const response = await fetch(`${API_BASE_URL}/Auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email, password }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Giriş yapılırken bir hata oluştu");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Login error:", error);
    throw error;
  }
}

/**
 * Check if user is authenticated with valid token
 */
export function isAuthenticated(): boolean {
  const token = localStorage.getItem("fbAdminToken");
  if (!token) return false;

  try {
    // Basic token validation - check if it's not expired
    const payload = JSON.parse(atob(token.split(".")[1]));
    const currentTime = Date.now() / 1000;

    if (payload.exp < currentTime) {
      // Token expired, remove it
      localStorage.removeItem("fbAdminToken");
      localStorage.removeItem("fbAdmin");
      return false;
    }

    return true;
  } catch (error) {
    console.error("Token validation error:", error);
    localStorage.removeItem("fbAdminToken");
    localStorage.removeItem("fbAdmin");
    return false;
  }
}

/**
 * Get current user info from token
 */
export function getCurrentUser() {
  const adminData = localStorage.getItem("fbAdmin");
  return adminData ? JSON.parse(adminData) : null;
}

/**
 * Get current user roles from token
 */
export function getCurrentUserRoles(): string[] {
  const token = localStorage.getItem("fbAdminToken");
  if (!token) return [];

  try {
    const payload = JSON.parse(atob(token.split(".")[1]));
    const roles =
      payload["http://schemas.microsoft.com/ws/2008/06/identity/claims/role"];
    return Array.isArray(roles) ? roles : [roles];
  } catch (error) {
    console.error("Get roles error:", error);
    return [];
  }
}

/**
 * Check if current user is admin
 */
export function isAdmin(): boolean {
  const roles = getCurrentUserRoles();
  return roles.includes("Admin");
}

/**
 * Check if current user is moderator
 */
export function isModerator(): boolean {
  const roles = getCurrentUserRoles();
  return roles.includes("Moderator");
}

/**
 * Logout admin user
 */
export function logoutAdmin(): void {
  localStorage.removeItem("fbAdminToken");
  localStorage.removeItem("fbAdmin");
}

/**
 * Fetch all ratings (Admin only)
 */
export async function fetchAllRatings() {
  try {
    const response = await fetchApi(`${API_BASE_URL}/Rating`, {
      method: "GET",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Puanlar alınırken bir hata oluştu");
    }

    return await response.json();
  } catch (error) {
    console.error("Fetch ratings error:", error);
    throw error;
  }
}

/**
 * Fetch ratings for a specific user (Admin only)
 */
export async function fetchUserRatings(userId: string) {
  try {
    const response = await fetchApi(`${API_BASE_URL}/Rating/user/${userId}`, {
      method: "GET",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Kullanıcı puanları alınırken bir hata oluştu"
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Fetch user ratings error:", error);
    throw error;
  }
}

/**
 * Fetch current moderator's ratings
 */
export async function fetchMyRatings() {
  try {
    const response = await fetchApi(`${API_BASE_URL}/Rating/my-ratings`, {
      method: "GET",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Puanlarım alınırken bir hata oluştu"
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Fetch my ratings error:", error);
    throw error;
  }
}

/**
 * Fetch current moderator's rating for a specific user
 */
export async function fetchMyRatingForUser(userId: string) {
  try {
    const response = await fetchApi(
      `${API_BASE_URL}/Rating/my-rating/${userId}`,
      {
        method: "GET",
      }
    );

    if (response.status === 404) {
      return null; // No rating found
    }

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Puan alınırken bir hata oluştu");
    }

    return await response.json();
  } catch (error) {
    console.error("Fetch my rating for user error:", error);
    throw error;
  }
}

/**
 * Create or update rating for a user
 */
export async function createOrUpdateRating(ratingData: {
  userId: string;
  toplaBeceri?: number;
  fizikselOzellik?: number;
  notes?: string;
  jumpHeight?: number;
  jumpRecordDate?: string;
  sprintTime?: number;
  sprintRecordDate?: string;
}) {
  try {
    const response = await fetchApi(`${API_BASE_URL}/Rating`, {
      method: "POST",
      body: JSON.stringify(ratingData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Puan kaydedilirken bir hata oluştu"
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Create/update rating error:", error);
    throw error;
  }
}

/**
 * Delete a rating (Admin or own rating for Moderator)
 */
export async function deleteRating(ratingId: string) {
  try {
    const response = await fetchApi(
      `${apiEndpoints.deleteRating}/${ratingId}`,
      {
        method: "DELETE",
      }
    );

    if (!response.ok) {
      let errorData = {};
      try {
        errorData = await response.json();
      } catch {}
      throw new Error(
        errorData.message || errorData.error || "Değerlendirme silinirken bir hata oluştu"
      );
    }

    // NoContent (204) için json yok, 204 ise null döndür
    if (response.status === 204) return null;
    return await response.json();
  } catch (error) {
    console.error("Delete rating error:", error);
    throw error;
  }
}

/**
 * Create a new moderator
 */
export async function createModerator(moderatorData: {
  fullName: string;
  email: string;
  password: string;
}) {
  try {
    const response = await fetchApi(`${API_BASE_URL}/Auth/create-moderator`, {
      method: "POST",
      body: JSON.stringify(moderatorData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Moderator oluşturulurken bir hata oluştu"
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Create moderator error:", error);
    throw error;
  }
}

/**
 * Get all moderators (Admin only)
 */
export async function fetchModerators() {
  try {
    const response = await fetchApi(`${API_BASE_URL}/Auth/moderators`, {
      method: "GET",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Moderatorler alınırken bir hata oluştu"
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Fetch moderators error:", error);
    throw error;
  }
}

/**
 * Delete a moderator (Admin only)
 */
export async function deleteModerator(moderatorId: string) {
  try {
    const response = await fetchApi(
      `${API_BASE_URL}/Auth/moderator/${moderatorId}`,
      {
        method: "DELETE",
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Moderator silinirken bir hata oluştu"
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Delete moderator error:", error);
    throw error;
  }
}

/**
 * Update a moderator (Admin only)
 */
export async function updateModerator(
  moderatorId: string,
  moderatorData: {
    fullName: string;
    email: string;
    password?: string;
  }
) {
  try {
    const response = await fetchApi(
      `${API_BASE_URL}/Auth/moderator/${moderatorId}`,
      {
        method: "PUT",
        body: JSON.stringify(moderatorData),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Moderator güncellenirken bir hata oluştu"
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Update moderator error:", error);
    throw error;
  }
}

/**
 * Fetch age-based statistics for applications
 */
export async function fetchAgeStatistics() {
  try {
    const response = await fetchApi(apiEndpoints.getAgeStatistics, {
      method: "GET",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || "Yaş istatistikleri alınırken bir hata oluştu"
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Fetch age statistics error:", error);
    throw error;
  }
}
