using backend.Models;
using backend.Models.DTOs;
using backend.Repositories.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace backend.Controllers;

/// <summary>
/// Controller for managing user data
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class UserController(IUserRepository userRepository) : ControllerBase
{
    private readonly IUserRepository _userRepository = userRepository;

    /// <summary>
    /// Checks if a user is an admin or moderator user
    /// </summary>
    /// <param name="user">The user to check</param>
    /// <returns>True if the user is an admin or moderator, false otherwise</returns>
    private static bool IsAdminOrModeratorUser(User user)
    {
        return user.Email?.Contains("@izefe.com") == true
            && (user.Email.StartsWith("admin@") || user.Email.StartsWith("moderator"));
    }

    /// <summary>
    /// Gets all users with their associated parent information
    /// </summary>
    /// <returns>A list of all users with parent details</returns>
    /// <response code="200">Returns the list of users</response>
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<UserWithParentDTO>>> GetUsers()
    {
        var users = await _userRepository.GetUsersWithParentsAsync();
        var filteredUsers = users.Where(u => !IsAdminOrModeratorUser(u));
        return Ok(Mapping.ToUserWithParentDTOs(filteredUsers));
    }

    /// <summary>
    /// Gets a specific user by ID with parent information
    /// </summary>
    /// <param name="id">The ID of the user to retrieve</param>
    /// <returns>The user with the specified ID</returns>
    /// <response code="200">Returns the requested user</response>
    /// <response code="404">If the user is not found</response>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<UserWithParentDTO>> GetUser(Guid id)
    {
        var user = await _userRepository.GetUserWithParentAsync(id);

        if (user == null || IsAdminOrModeratorUser(user))
        {
            return NotFound();
        }

        return Ok(Mapping.ToUserWithParentDTO(user));
    }

    /// <summary>
    /// Gets a user by their tracking code
    /// </summary>
    /// <param name="code">The tracking code of the user to retrieve</param>
    /// <returns>The user with the specified tracking code</returns>
    /// <response code="200">Returns the requested user</response>
    /// <response code="404">If the user is not found</response>
    [HttpGet("tracking/{code}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<UserWithParentDTO>> GetUserByTrackingCode(string code)
    {
        var user = await _userRepository.GetUserByTrackingCodeAsync(code);

        if (user == null || IsAdminOrModeratorUser(user))
        {
            return NotFound();
        }

        return Ok(Mapping.ToUserWithParentDTO(user));
    }

    /// <summary>
    /// Updates a specific user
    /// </summary>
    /// <param name="id">The ID of the user to update</param>
    /// <param name="userDto">The updated user data</param>
    /// <returns>No content</returns>
    /// <response code="204">If the update was successful</response>
    /// <response code="400">If the ID in the URL doesn't match the ID in the user object</response>
    /// <response code="404">If the user is not found</response>
    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> PutUser(Guid id, UserWithParentDTO userDto)
    {
        if (id != userDto.Id)
        {
            return BadRequest();
        }

        try
        {
            var existingUser = await _userRepository.GetUserWithParentAsync(id);
            if (existingUser == null)
            {
                return NotFound();
            }

            // Update user properties from DTO
            Mapping.UpdateUserFromDTO(existingUser, userDto);

            // Only update ParentId if it's different
            if (userDto.ParentId != existingUser.ParentId)
            {
                existingUser.ParentId = userDto.ParentId;
            }

            await _userRepository.UpdateAsync(existingUser);
            await _userRepository.SaveAsync();
        }
        catch (Exception ex)
        {
            if (!await _userRepository.ExistsAsync(id))
            {
                return NotFound();
            }
            else
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        return NoContent();
    }

    /// <summary>
    /// Creates a new user
    /// </summary>
    /// <param name="userDto">The user to create</param>
    /// <returns>The newly created user</returns>
    /// <response code="201">Returns the newly created user</response>
    /// <response code="400">If the user data is invalid</response>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<UserWithParentDTO>> PostUser(UserWithParentDTO userDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        // Create a new User entity from the DTO using mapping approach
        var user = new User { ParentId = userDto.ParentId != Guid.Empty ? userDto.ParentId : null };

        // Use mapping to update user properties from DTO
        Mapping.UpdateUserFromDTO(user, userDto);

        // Calculate and set the TrackingSequence based on birth year
        // Each birth year starts from 1 and increments for each user born in that year
        var birthYear = user.DateOfBirth.Year;
        user.TrackingSequence = await _userRepository.GetNextTrackingSequenceForBirthYearAsync(
            birthYear
        );

        await _userRepository.AddAsync(user);
        await _userRepository.SaveAsync();

        // Get the complete user with parent for the response
        var createdUser = await _userRepository.GetUserWithParentAsync(user.Id);
        var createdUserDto = Mapping.ToUserWithParentDTO(createdUser);

        return CreatedAtAction(nameof(GetUser), new { id = user.Id }, createdUserDto);
    }

    /// <summary>
    /// Deletes a specific user
    /// </summary>
    /// <param name="id">The ID of the user to delete</param>
    /// <returns>No content</returns>
    /// <response code="204">If the deletion was successful</response>
    /// <response code="404">If the user is not found</response>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeleteUser(Guid id)
    {
        var user = await _userRepository.GetByIdAsync(id);
        if (user == null)
        {
            return NotFound();
        }

        await _userRepository.DeleteUserWithParentAsync(id);
        await _userRepository.SaveAsync();

        return NoContent();
    }
}
