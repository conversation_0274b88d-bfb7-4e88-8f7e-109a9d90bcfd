using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using backend.Models;
using backend.Models.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;

namespace backend.Controllers;

[ApiController]
[Route("api/[controller]")]
[AllowAnonymous]
public class AuthController(UserManager<User> userManager, IConfiguration configuration)
    : ControllerBase
{
    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] LoginDTO loginDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var user = await userManager.FindByEmailAsync(loginDto.Email);

        if (user == null || !await userManager.CheckPasswordAsync(user, loginDto.Password))
        {
            return Unauthorized(new { message = "Invalid email or password" });
        }

        var token = await GenerateJwtToken(user);
        var roles = await userManager.GetRolesAsync(user);

        return Ok(
            new
            {
                token,
                roles,
                user = new
                {
                    user.Id,
                    user.FullName,
                    user.Email,
                },
            }
        );
    }

    [HttpPost("create-moderator")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> CreateModerator(
        [FromBody] CreateModeratorDTO createModeratorDto
    )
    {
        // Oluşturma işlemi için şifre zorunlu
        if (string.IsNullOrEmpty(createModeratorDto.Password))
        {
            return BadRequest(new { message = "Şifre gereklidir" });
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        // Check if email already exists
        var existingUser = await userManager.FindByEmailAsync(createModeratorDto.Email);
        if (existingUser != null)
        {
            return BadRequest(new { message = "Bu e-posta adresi zaten kullanılıyor" });
        }

        var moderatorUser = new User
        {
            UserName = createModeratorDto.Email,
            FullName = createModeratorDto.FullName,
            Email = createModeratorDto.Email,
            EmailConfirmed = true,
            ClubName = "izefe",
            DateOfBirth = new DateTime(1990, 1, 1, 0, 0, 0, DateTimeKind.Utc),
            ParentId = null,
            HasHealthIssues = false,
            HealthIssues = "",
        };

        var result = await userManager.CreateAsync(moderatorUser, createModeratorDto.Password);

        if (result.Succeeded)
        {
            await userManager.AddToRoleAsync(moderatorUser, "Moderator");

            return Ok(
                new
                {
                    message = "Moderator başarıyla oluşturuldu",
                    user = new
                    {
                        moderatorUser.Id,
                        moderatorUser.FullName,
                        moderatorUser.Email,
                    },
                }
            );
        }

        return BadRequest(
            new { message = "Moderator oluşturulurken hata oluştu", errors = result.Errors }
        );
    }

    [HttpGet("moderators")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetModerators()
    {
        var moderators = await userManager.GetUsersInRoleAsync("Moderator");

        var moderatorList = moderators
            .Select(m => new
            {
                m.Id,
                m.FullName,
                m.Email,
                m.UserName,
            })
            .ToList();

        return Ok(moderatorList);
    }

    [HttpDelete("moderator/{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> DeleteModerator(Guid id)
    {
        var moderator = await userManager.FindByIdAsync(id.ToString());

        if (moderator == null)
        {
            return NotFound(new { message = "Moderator bulunamadı" });
        }

        var roles = await userManager.GetRolesAsync(moderator);
        if (!roles.Contains("Moderator"))
        {
            return BadRequest(new { message = "Bu kullanıcı bir moderator değil" });
        }

        var result = await userManager.DeleteAsync(moderator);

        if (result.Succeeded)
        {
            return Ok(new { message = "Moderator başarıyla silindi" });
        }

        return BadRequest(
            new { message = "Moderator silinirken hata oluştu", errors = result.Errors }
        );
    }

    [HttpPut("moderator/{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<IActionResult> UpdateModerator(Guid id, [FromBody] CreateModeratorDTO updateModeratorDto)
    {
        // Güncelleme için şifre kontrolünü manuel yapalım
        if (!string.IsNullOrEmpty(updateModeratorDto.Password) && updateModeratorDto.Password.Length < 6)
        {
            return BadRequest(new { message = "Şifre en az 6 karakter olmalıdır" });
        }

        // Şifre alanını validasyondan çıkarıp manuel kontrol edelim
        ModelState.Remove("Password");
        
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var moderator = await userManager.FindByIdAsync(id.ToString());
        if (moderator == null)
        {
            return NotFound(new { message = "Moderator bulunamadı" });
        }

        var roles = await userManager.GetRolesAsync(moderator);
        if (!roles.Contains("Moderator"))
        {
            return BadRequest(new { message = "Bu kullanıcı bir moderator değil" });
        }

        // Check if email is being changed and if it's already in use
        if (moderator.Email != updateModeratorDto.Email)
        {
            var existingUser = await userManager.FindByEmailAsync(updateModeratorDto.Email);
            if (existingUser != null && existingUser.Id != moderator.Id)
            {
                return BadRequest(new { message = "Bu e-posta adresi zaten kullanılıyor" });
            }
        }

        // Update moderator properties
        moderator.FullName = updateModeratorDto.FullName;
        moderator.Email = updateModeratorDto.Email;
        moderator.UserName = updateModeratorDto.Email;

        var updateResult = await userManager.UpdateAsync(moderator);
        if (!updateResult.Succeeded)
        {
            return BadRequest(new { message = "Moderator güncellenirken hata oluştu", errors = updateResult.Errors });
        }

        // Update password if provided
        if (!string.IsNullOrEmpty(updateModeratorDto.Password))
        {
            var token = await userManager.GeneratePasswordResetTokenAsync(moderator);
            var passwordResult = await userManager.ResetPasswordAsync(moderator, token, updateModeratorDto.Password);
            
            if (!passwordResult.Succeeded)
            {
                return BadRequest(new { message = "Şifre güncellenirken hata oluştu", errors = passwordResult.Errors });
            }
        }

        return Ok(new
        {
            message = "Moderator başarıyla güncellendi",
            user = new
            {
                moderator.Id,
                moderator.FullName,
                moderator.Email,
            },
        });
    }

    private async Task<string> GenerateJwtToken(User user)
    {
        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new Claim(ClaimTypes.Email, user.Email ?? string.Empty),
            new Claim("fullName", user.FullName ?? string.Empty),
        };

        // Add roles if user has any
        var roles = await userManager.GetRolesAsync(user);
        foreach (var role in roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role));
        }

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["Jwt:Key"]));
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
        var expiryMinutes = configuration.GetValue<double>("Jwt:ExpiryMinutes");
        var expires = DateTime.UtcNow.AddMinutes(expiryMinutes);

        var token = new JwtSecurityToken(
            configuration["Jwt:Issuer"],
            configuration["Jwt:Audience"],
            claims,
            expires: expires,
            signingCredentials: creds
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }
}
