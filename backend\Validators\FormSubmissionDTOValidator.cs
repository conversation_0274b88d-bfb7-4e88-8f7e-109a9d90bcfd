using backend.Controllers;
using backend.Models.DTOs;
using FluentValidation;

namespace backend.Validators
{
    public class FormSubmissionDTOValidator : AbstractValidator<FormSubmissionDTO>
    {
        public FormSubmissionDTOValidator()
        {
            RuleFor(x => x.User).SetValidator(new UserWithParentDTOValidator());
            RuleFor(x => x.Parent).SetValidator(new ParentDTOValidator());
        }
    }

    public class UserWithParentDTOValidator : AbstractValidator<UserWithParentDTO>
    {
        public UserWithParentDTOValidator()
        {
            RuleFor(x => x.FullName)
                .NotEmpty()
                .When(x => !string.IsNullOrEmpty(x.FullName))
                .WithMessage("Ad ve soyad gereklidir");
            RuleFor(x => x.DateOfBirth)
                .NotEmpty()
                .WithMessage("Doğum tarihi gereklidir")
                .Must(birthDate => 
                {
                    var birthYear = birthDate.Year;
                    return birthYear >= 2011 && birthYear <= 2016;
                })
                .WithMessage("Başvuru yaş aralığı: Sadece 2011-2016 arası doğumlular başvuru yapabilir.");
            RuleFor(x => x.PhoneNumber)
                .NotEmpty()
                .When(x => !string.IsNullOrEmpty(x.PhoneNumber))
                .WithMessage("Telefon numarası gereklidir");
            RuleFor(x => x.Email)
                .NotEmpty()
                .EmailAddress()
                .When(x => !string.IsNullOrEmpty(x.Email))
                .WithMessage("Geçerli bir e-posta adresi giriniz");
        }
    }

    public class ParentDTOValidator : AbstractValidator<ParentDTO>
    {
        public ParentDTOValidator()
        {
            RuleFor(x => x.FullName)
                .NotEmpty()
                .When(x => !string.IsNullOrEmpty(x.FullName))
                .WithMessage("Ad ve soyad gereklidir");
            RuleFor(x => x.MobilePhone)
                .NotEmpty()
                .When(x => !string.IsNullOrEmpty(x.MobilePhone))
                .WithMessage("Cep telefonu gereklidir");
            RuleFor(x => x.RelationshipType)
                .NotEmpty()
                .When(x => !string.IsNullOrEmpty(x.RelationshipType))
                .WithMessage("Yakınlık derecesi gereklidir");
        }
    }
}
