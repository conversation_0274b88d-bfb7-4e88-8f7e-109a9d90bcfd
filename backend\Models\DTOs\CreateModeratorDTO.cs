using System.ComponentModel.DataAnnotations;

namespace backend.Models.DTOs;

/// <summary>
/// DTO for creating or updating a moderator user
/// </summary>
public class CreateModeratorDTO
{
    /// <summary>
    /// The full name of the moderator
    /// </summary>
    [Required(ErrorMessage = "Ad Soyad gereklidir")]
    [StringLength(100, ErrorMessage = "Ad Soyad en fazla 100 karakter olabilir")]
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// The email address of the moderator
    /// </summary>
    [Required(ErrorMessage = "E-posta gereklidir")]
    [EmailAddress(ErrorMessage = "Geçerli bir e-posta adresi giriniz")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// The password for the moderator account (optional for updates)
    /// </summary>
    [StringLength(100, MinimumLength = 6, ErrorMessage = "<PERSON><PERSON>re en az 6 karakter olmalıdır")]
    public string? Password { get; set; } = null;
}
