"use client";

import * as React from "react";
import { Check, ChevronDown } from "lucide-react";

import { cn } from "@/lib/utils";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";

interface SelectProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  children: React.ReactNode;
  disabled?: boolean;
}

interface SelectItemProps {
  value: string;
  children: React.ReactNode;
  onSelect?: (value: string) => void;
}

const SelectContext = React.createContext<{
  value?: string;
  onValueChange?: (value: string) => void;
  open: boolean;
  setOpen: (open: boolean) => void;
}>({
  open: false,
  setOpen: () => {},
});

export function Select({
  value,
  onValueChange,
  placeholder,
  children,
  disabled,
}: SelectProps) {
  const [open, setOpen] = React.useState(false);

  const getSelectedLabel = () => {
    const childrenArray = React.Children.toArray(children);
    const selectedChild = childrenArray.find((child) => {
      if (React.isValidElement(child) && child.props.value === value) {
        return true;
      }
      return false;
    });

    if (React.isValidElement(selectedChild)) {
      return selectedChild.props.children;
    }

    return placeholder;
  };

  return (
    <SelectContext.Provider value={{ value, onValueChange, open, setOpen }}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-transparent"
            disabled={disabled}
          >
            {value ? (
              getSelectedLabel()
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <div className="max-h-60 overflow-y-auto p-1">{children}</div>
        </PopoverContent>
      </Popover>
    </SelectContext.Provider>
  );
}

export function SelectItem({ value, children, onSelect }: SelectItemProps) {
  const {
    value: selectedValue,
    onValueChange,
    setOpen,
  } = React.useContext(SelectContext);
  const isSelected = selectedValue === value;

  const handleSelect = () => {
    onValueChange?.(value);
    onSelect?.(value);
    setOpen(false);
  };

  return (
    <div
      className={cn(
        "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
        isSelected && "bg-accent text-accent-foreground"
      )}
      onClick={handleSelect}
    >
      <Check
        className={cn("mr-2 h-4 w-4", isSelected ? "opacity-100" : "opacity-0")}
      />
      {children}
    </div>
  );
}

// Legacy exports for compatibility
export const SelectValue = () => null;
export const SelectTrigger = ({ children }: { children: React.ReactNode }) =>
  children;
export const SelectContent = ({ children }: { children: React.ReactNode }) =>
  children;
